import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TodoItem } from './TodoItem';
import { Todo } from '@shared/types';
import { todoService } from '@renderer/services/todo.service';

export const TodoList: React.FC = () => {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTodos();
  }, []);

  const loadTodos = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await todoService.getAllTodos();
      setTodos(result.data);
    } catch (err) {
      console.error('Failed to load todos:', err);
      setError(err instanceof Error ? err.message : 'Failed to load todos');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTodoUpdate = (updatedTodo: Todo) => {
    setTodos(prevTodos =>
      prevTodos.map(todo =>
        todo.id === updatedTodo.id ? updatedTodo : todo
      )
    );
  };

  const handleTodoDelete = (todoId: string) => {
    setTodos(prevTodos => prevTodos.filter(todo => todo.id !== todoId));
  };

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-fa-blue-200 border-t-fa-blue-500 rounded-full animate-spin mx-auto mb-4" />
          <p className="fa-body text-fa-gray-500">Loading todos...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <div className="text-5xl mb-4">⚠️</div>
          <h3 className="fa-heading-3 mb-2 text-fa-error">Error Loading Todos</h3>
          <p className="fa-body text-fa-gray-500 mb-4">{error}</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={loadTodos}
            className="fa-button-primary px-4 py-2"
          >
            Try Again
          </motion.button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto">
      <div className="space-y-3">
        {todos.map((todo, index) => (
          <motion.div
            key={todo.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <TodoItem
              todo={todo}
              onUpdate={handleTodoUpdate}
              onDelete={handleTodoDelete}
            />
          </motion.div>
        ))}
      </div>

      {todos.length === 0 && (
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <div className="text-5xl mb-4">🍃</div>
            <h3 className="fa-heading-3 mb-2">No tasks yet</h3>
            <p className="fa-body text-fa-gray-500">
              Add your first task to get started!
            </p>
          </div>
        </div>
      )}
    </div>
  );
};